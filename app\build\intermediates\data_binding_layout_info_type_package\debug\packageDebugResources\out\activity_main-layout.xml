<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.yancao.qrscanner" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="185" endOffset="51"/></Target><Target id="@+id/preview_view" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="9" startOffset="4" endLine="16" endOffset="51"/></Target><Target id="@+id/qr_overlay_view" view="com.yancao.qrscanner.ui.QrCodeOverlayView"><Expressions/><location startLine="19" startOffset="4" endLine="27" endOffset="62"/></Target><Target id="@+id/btn_flashlight" view="Button"><Expressions/><location startLine="30" startOffset="4" endLine="41" endOffset="62"/></Target><Target id="@+id/zoom_control_panel" view="LinearLayout"><Expressions/><location startLine="44" startOffset="4" endLine="123" endOffset="18"/></Target><Target id="@+id/tv_zoom_ratio" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="64" endOffset="45"/></Target><Target id="@+id/tv_zoom_range" view="TextView"><Expressions/><location startLine="66" startOffset="8" endLine="74" endOffset="47"/></Target><Target id="@+id/btn_zoom_out" view="Button"><Expressions/><location startLine="82" startOffset="12" endLine="90" endOffset="48"/></Target><Target id="@+id/btn_zoom_reset" view="Button"><Expressions/><location startLine="92" startOffset="12" endLine="100" endOffset="55"/></Target><Target id="@+id/btn_zoom_in" view="Button"><Expressions/><location startLine="102" startOffset="12" endLine="110" endOffset="50"/></Target><Target id="@+id/seek_bar_zoom" view="SeekBar"><Expressions/><location startLine="115" startOffset="8" endLine="121" endOffset="34"/></Target><Target id="@+id/control_panel" view="LinearLayout"><Expressions/><location startLine="126" startOffset="4" endLine="183" endOffset="18"/></Target><Target id="@+id/tv_scan_results" view="TextView"><Expressions/><location startLine="138" startOffset="8" endLine="149" endOffset="37"/></Target><Target id="@+id/button_container" view="LinearLayout"><Expressions/><location startLine="152" startOffset="8" endLine="181" endOffset="22"/></Target><Target id="@+id/btn_scan" view="Button"><Expressions/><location startLine="160" startOffset="12" endLine="168" endOffset="58"/></Target><Target id="@+id/btn_broadcast" view="Button"><Expressions/><location startLine="171" startOffset="12" endLine="179" endOffset="58"/></Target></Targets></Layout>