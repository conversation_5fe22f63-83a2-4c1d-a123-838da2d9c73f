package com.yancao.qrscanner.viewModel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData

/**
 * 相机控制ViewModel
 *
 * 负责管理相机的控制功能，包括：
 * 1. 闪光灯状态管理
 * 2. 缩放功能管理
 * 3. 与UI层的数据绑定
 *
 * 遵循MVVM架构，将相机控制逻辑与UI分离
 */
class CameraControlViewModel(application: Application) : AndroidViewModel(application) {

    // 闪光灯状态 LiveData
    val isFlashlightEnabled = MutableLiveData(false)

    // 缩放比例 LiveData
    val zoomRatio = MutableLiveData(1.0f)

    // 最小缩放比例
    val minZoomRatio = MutableLiveData(1.0f)

    // 最大缩放比例
    val maxZoomRatio = MutableLiveData(1.0f)

    // 是否支持闪光灯
    val hasFlashlight = MutableLiveData(false)

    /**
     * 切换闪光灯状态
     * @return 新的闪光灯状态
     */
    fun toggleFlashlight(): Boolean {
        val newState = !(isFlashlightEnabled.value ?: false)
        isFlashlightEnabled.value = newState
        return newState
    }

    /**
     * 设置闪光灯状态
     * @param enabled 是否启用闪光灯
     */
    fun setFlashlightEnabled(enabled: Boolean) {
        isFlashlightEnabled.value = enabled
    }

    /**
     * 更新缩放比例
     * @param ratio 新的缩放比例
     */
    fun updateZoomRatio(ratio: Float) {
        val clampedRatio = ratio.coerceIn(
            minZoomRatio.value ?: 1.0f,
            maxZoomRatio.value ?: 1.0f
        )
        zoomRatio.value = clampedRatio
    }

    /**
     * 设置缩放范围
     * @param min 最小缩放比例
     * @param max 最大缩放比例
     */
    fun setZoomRange(min: Float, max: Float) {
        minZoomRatio.value = min
        maxZoomRatio.value = max

        // 确保当前缩放比例在有效范围内
        val currentRatio = zoomRatio.value ?: 1.0f
        if (currentRatio < min || currentRatio > max) {
            zoomRatio.value = currentRatio.coerceIn(min, max)
        }
    }

    /**
     * 设置是否支持闪光灯
     * @param supported 是否支持闪光灯
     */
    fun setFlashlightSupported(supported: Boolean) {
        hasFlashlight.value = supported

        // 如果不支持闪光灯，确保闪光灯状态为关闭
        if (!supported) {
            isFlashlightEnabled.value = false
        }
    }

    /**
     * 重置到默认状态
     */
    fun resetToDefaults() {
        isFlashlightEnabled.value = false
        zoomRatio.value = 1.0f
    }

    /**
     * 增加缩放（放大）
     * @param step 缩放步长，默认0.5
     */
    fun zoomIn(step: Float = 0.5f) {
        val currentRatio = zoomRatio.value ?: 1.0f
        val maxRatio = maxZoomRatio.value ?: 1.0f
        val newRatio = (currentRatio + step).coerceAtMost(maxRatio)
        zoomRatio.value = newRatio
    }

    /**
     * 减少缩放（缩小）
     * @param step 缩放步长，默认0.5
     */
    fun zoomOut(step: Float = 0.5f) {
        val currentRatio = zoomRatio.value ?: 1.0f
        val minRatio = minZoomRatio.value ?: 1.0f
        val newRatio = (currentRatio - step).coerceAtLeast(minRatio)
        zoomRatio.value = newRatio
    }

    /**
     * 重置缩放到1.0
     */
    fun resetZoom() {
        zoomRatio.value = 1.0f
    }
}