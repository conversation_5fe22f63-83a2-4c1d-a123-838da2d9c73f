<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.QrScanActivity">

    <!-- 摄像头预览组件 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/preview_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/control_panel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 二维码覆盖层 -->
    <com.yancao.qrscanner.ui.QrCodeOverlayView
        android:id="@+id/qr_overlay_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/preview_view"
        app:layout_constraintEnd_toEndOf="@+id/preview_view"
        app:layout_constraintStart_toStartOf="@+id/preview_view"
        app:layout_constraintTop_toTopOf="@+id/preview_view" />

    <!-- 新增：闪光灯按钮 - 浮动在预览视图右上角 -->
    <Button
        android:id="@+id/btn_flashlight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="开启闪光灯"
        android:textSize="12sp"
        android:background="@android:color/holo_orange_light"
        android:textColor="@android:color/white"
        android:padding="8dp"
        app:layout_constraintEnd_toEndOf="@+id/preview_view"
        app:layout_constraintTop_toTopOf="@+id/preview_view" />

    <!-- 新增：缩放控制面板 - 浮动在预览视图左侧 -->
    <LinearLayout
        android:id="@+id/zoom_control_panel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:orientation="vertical"
        android:background="@android:color/black"
        android:padding="8dp"
        android:alpha="0.8"
        app:layout_constraintStart_toStartOf="@+id/preview_view"
        app:layout_constraintTop_toTopOf="@+id/preview_view">

        <!-- 缩放信息显示 -->
        <TextView
            android:id="@+id/tv_zoom_ratio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="缩放: 1.0x"
            android:textColor="@android:color/white"
            android:textSize="10sp"
            android:layout_gravity="center" />

        <TextView
            android:id="@+id/tv_zoom_range"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="范围: 1.0x - 10.0x"
            android:textColor="@android:color/white"
            android:textSize="8sp"
            android:layout_gravity="center"
            android:layout_marginBottom="4dp" />

        <!-- 缩放按钮 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_zoom_out"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:text="-"
                android:textSize="12sp"
                android:background="@android:color/holo_blue_light"
                android:textColor="@android:color/white"
                android:layout_marginEnd="2dp" />

            <Button
                android:id="@+id/btn_zoom_reset"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:text="1x"
                android:textSize="8sp"
                android:background="@android:color/holo_green_light"
                android:textColor="@android:color/white"
                android:layout_marginHorizontal="2dp" />

            <Button
                android:id="@+id/btn_zoom_in"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:text="+"
                android:textSize="12sp"
                android:background="@android:color/holo_blue_light"
                android:textColor="@android:color/white"
                android:layout_marginStart="2dp" />

        </LinearLayout>

        <!-- 缩放滑动条 -->
        <SeekBar
            android:id="@+id/seek_bar_zoom"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:max="100"
            android:progress="0" />

    </LinearLayout>

    <!-- 控制面板 -->
    <LinearLayout
        android:id="@+id/control_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/black"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 扫描结果显示区域 -->
        <TextView
            android:id="@+id/tv_scan_results"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@android:color/black"
            android:textColor="@android:color/white"
            android:padding="8dp"
            android:text="扫描结果将显示在这里..."
            android:textSize="12sp"
            android:maxLines="3"
            android:ellipsize="end" />

        <!-- 按钮容器 -->
        <LinearLayout
            android:id="@+id/button_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- 扫描按钮 -->
            <Button
                android:id="@+id/btn_scan"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="开始扫描"
                android:background="@android:color/holo_blue_light"
                android:textColor="@android:color/white" />

            <!-- 广播按钮 -->
            <Button
                android:id="@+id/btn_broadcast"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="广播结果"
                android:background="@android:color/holo_green_light"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>