/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel, +androidx.camera.core.ImageAnalysis.Analyzer) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel) (androidx.appcompat.app.AppCompatActivity android.view.View, +androidx.camera.core.ImageAnalysis.Analyzer) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel android.view.View, +androidx.camera.core.ImageAnalysis.Analyzer android.view.View) (androidx.appcompat.app.AppCompatActivity android.view.View android.view.View) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel android.view.View android.view.View android.view.View) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity, +androidx.camera.core.ImageAnalysis.Analyzer, +androidx.camera.core.ImageAnalysis.Analyzer$ #androidx.lifecycle.AndroidViewModel, +androidx.camera.core.ImageAnalysis.Analyzer) (androidx.appcompat.app.AppCompatActivity$ #androidx.lifecycle.AndroidViewModel